import React, { useState, useEffect } from 'react';
import { Formik, Form } from 'formik';
import {
  Container,
  Typography,
  TextField,
  Checkbox,
  FormControlLabel,
  Button,
  Grid,
  Paper,
  Box,
  FormGroup,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Radio,
  RadioGroup,
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import api, { getInspectionsWithOffline } from '../services/api';
import { isOnline } from '../utils/offlineUtils';

// Image compression utility
const compressImage = (file, maxWidth = 1200, quality = 0.8) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      canvas.toBlob(resolve, 'image/jpeg', quality);
    };

    img.src = URL.createObjectURL(file);
  });
};

const initialValues = {
  fiche_num: '',
  fiche_generation_method: 'manual',
  date: '',
  projet: '',
  equipement: '',
  equipement_custom: '',
  tag: '',
  constructeur: '',
  model: '',
  numero_serie: '',
  date_installation: '',
  age: '',
  puissance: '',
  courant: '',
  tension: '',
  unite: '',
  unite_custom: '',
  localisation: '',
  zone_atex: '',
  groupe_gaz: '',
  classe_t: '',
  marquage_atex_g: '',
  marquage_atex_d: '',
  marquage_us: '',
  type_marquage: '',
  mode_protection: '',
  organisme_notifie: '',
  ip: '',
  nema: '',
  certificat: '',
  tamb_min: '',
  tamb_max: '',
  atex_oui: false,
  atex_non: false,
  acces_inaccessible: false,
  acces_calorifuge: false,
  acces_peinte: false,
  acces_inaccessible_plaque: false,
  acces_illisible: false,
  acces_pas_plaque: false,
  // Niveau selection
  niveau_1: false,
  niveau_2: false,
  niveau_3: false,
  points: {

    //les installations Ex “d”, Ex “e”, Ex “n” et Ex “t/tD”
    // GÉNÉRALITÉS (TOUS LES MATÉRIELS)
    mat_approprié: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    groupe_correct: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    classe_temp: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    temp_surface: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    degre_protection: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    identification_circuit: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    enveloppe_verre: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    modification_non_autorisee: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    boulons_entrees: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    couvercles_filetes: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    dispositifs_respiration: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    identification_correcte: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    surfaces_joints: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    etat_garnitures: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    penetration_eau: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    dimensions_espaces: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    connexions_serrees: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    bornes_non_utilisees: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    dispositifs_scellement: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    composants_encapsules: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    composants_ignifuges: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    enveloppes_respiration: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    port_essai: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    operation_respiration: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

    // MATÉRIEL PARTICULIER (ÉCLAIRAGE)
    lampes_fluo: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    lampes_dhi: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    type_lampes: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

    // MATÉRIEL PARTICULIER (MOTEURS)
    distance_ventilateur: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    circulation_air: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    resistance_isolement: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    
    // INSTALLATION - GÉNÉRALITÉS
    dommage_cables: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    obturation_traversees: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    liaisons_terre: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    installation_variable: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    engorgements_joints: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    type_cable: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    boitiers_arret: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    integrite_conduits: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    impedance_boucle: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    protection_electrique: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    protection_electrique_chauffage: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    conditions_utilisation: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    extremites_cables: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    
    // INSTALLATION - SYSTÈMES DE CHAUFFAGE
    reglage_coupure: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    reinitialisation_coupure: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    reinitialisation_automatique: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    capteurs_temperature: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    dispositifs_coupure: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    reinitialisation_defaut: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    coupure_independante: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    capteur_niveau: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    capteur_debit: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

    //Moteurs 
    protection_moteur: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    
    // ENVIRONNEMENT
    protection_materiel: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    accumulation_poussiere: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
    isolation_electrique: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

// Les installations Ex “i //
// MATERIEL
  doc_materiel_appropriate: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  materiel_specifie: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  categorie_groupe_correct: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  degre_ip_groupe3: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  classe_temp_materiel: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  plage_temp_ambiante: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
 plage_temp_service: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  installation_reperee: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  presse_etoupes_type: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  
  modification_non_autorisee_i: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  barrieres_securite: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  tension_max_um: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  enveloppe_verre_i: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  pas_modif_non_autorisee: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  etat_garnitures_env: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  connexions_serrees_i: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  cartes_propres: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

  // INSTALLATION
  dommage_cables_i: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  obturation_traversees_i: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  cables_documentation: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  ecrans_documentation: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  connexions_point_point: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  continuite_terre: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  liaison_terre_integrite: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  mise_a_la_terre_sic: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  resistance_isolement_i: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  separation_circuits: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  protection_cc_alim: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  conditions_utilisation_i: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  extremites_cables_prot: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

  // ENVIRONNEMENT
  protection_materiel_i: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
  accumulation_poussiere_ext: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },





//Les installations Ex “o”



// ÉQUIPMENT
mat_approprie_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
groupe_correct_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
classe_temp_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
identification_circuit_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
enveloppe_verre_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
modification_non_autorisee_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
boulons_entrees_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
enveloppes_scellees_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
critere_liquide_protection: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
auge_position_mesure: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
fonctionnement_tele_signalisation: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
programme_nettoyage_liquide: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
identification_circuits_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
pas_modif_non_autorisee_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
connexions_serrees_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
etat_joints_etancheite: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
dispositifs_respiration_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
surpression_enveloppe: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
niveau_liquide_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

// INSTALLATION - GÉNÉRALITÉS
dommage_cables_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
obturation_traversees_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
liaisons_terre_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
installation_variable_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
type_cable_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
boitiers_coupe_feu: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
integrite_conduits_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
impedance_boucle_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
protection_electrique_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
protection_electrique_autorisee: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
conditions_utilisation_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
extremites_cables_prot_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

// INSTALLATION – SYSTÈMES DE CHAUFFAGE
reglage_coupure_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
reinitialisation_coupure_outil: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
reinitialisation_auto_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
capteurs_temperature_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
dispositifs_coupure_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
reinitialisation_defaut_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
coupure_independante_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
capteur_niveau_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
capteur_debit_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

// ENVIRONNEMENT
protection_materiel_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
accumulation_poussiere_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
isolation_electrique_o: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },



//les installations Ex “p” et “pD
// MATERIEL
mat_approprie_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
groupe_correct_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
classe_temp_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
identification_circuit_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
enveloppe_verre_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
modification_non_autorisee_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
identification_circuit_correcte_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
pas_modif_non_autorisee_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
type_lampes_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

// INSTALLATION
dommage_cables_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
liaisons_terre_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
conduites_tubes_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
gaz_impuretes_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
pression_debit_gaz: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
type_cable_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
impedance_boucle_it: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
protection_electrique_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
protection_reglee_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
temp_entree_gaz: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
barrieres_etincelles: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
conditions_utilisation_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },

// ENVIRONNEMENT
protection_materiel_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },
accumulation_poussiere_p: { correct: false, defaut: false, remarques: '', f: '', g: '', c: '' },





  },
  action: '',

  // New fields for inspector information and previous inspection
  date_precedente_inspection: '',
  inspecteur: '',
  qualifications: '',
  numero_certificat: '',
  observations_complementaires: '',

  // Company information
  inspected_company_name: '',

  // Note: Photo fields are NOT included in initialValues to prevent them from being
  // included in FormData when empty. They are handled separately in the form submission.
};

const pointsSections = [
  {
    title: 'Les installations Ex "d", Ex "e", Ex "n" et Ex "t/tD" (Tableau 01 l’IEC 60079-17V2023)',
    subsections: [
      {
        subtitle: 'GÉNÉRALITÉS (TOUS LES MATÉRIELS)',
        points: [
          { key: 'mat_approprié', label: 'Le matériel est approprié aux exigences relatives au niveau de protection/zone de l\'emplacement concerné', niveaux: [] },
          { key: 'groupe_correct', label: 'Le groupe de matériel est correct', niveaux: [2,3] },
          { key: 'classe_temp', label: (
              <>
                La classe de température du matériel est correcte (uniquement pour les gaz) <strong>(n)</strong>
              </>
            ), niveaux: [2,3] },
          { key: 'temp_surface', label: 'La température de surface maximale du matériel est correcte ' , niveaux: [2,3] },
          { key: 'degre_protection', label: 'Le degré de protection (degré IP) du matériel est approprié au niveau de protection/groupe/conductivité', niveaux: [] },
          { key: 'identification_circuit', label: 'L\'identification du circuit du matériel est disponible', niveaux: [] },
          { key: 'enveloppe_verre', label: 'L\'enveloppe, les parties en verre et les garnitures et/ou les matériaux d\'étanchéité verre sur métal sont satisfaisants', niveaux: [] },
          { key: 'modification_non_autorisee', label: 'Il n\'y a pas de modification non autorisée visible', niveaux: [1,2] },
          {
      key: 'boulons_entrees',
      label: (
        <>
          Les boulons, les dispositifs d'entrées de câbles (directes et indirectes) et les éléments d'obturation sont d'un type correct et sont complets et serrés <br />
          – vérification physique <br />
          – vérification visuelle
        </>
      ),
      niveaux: []
    },
         {
  key: 'couvercles_filetes',
  label: (
    <>
      Les couvercles filetés sur les enveloppes sont du type correct et sont serrés et fixés solidement <br />
      – vérification physique <br />
      – vérification visuelle
    </>
  ),
  niveaux: []
},
          { key: 'dispositifs_respiration', label: 'Les dispositifs de respiration et de drainage sont satisfaisants (d,e,n)', niveaux: [2,3] },
          { key: 'identification_correcte', label: 'L\'identification du circuit du matériel est correcte', niveaux: [3] },
          { key: 'surfaces_joints', label: 'Les surfaces des joints plans sont propres et non endommagées et les garnitures éventuelles sont satisfaisantes et correctement positionnées (d)', niveaux: [3] },
          { key: 'etat_garnitures', label: 'L\'état des garnitures des enveloppes est satisfaisant (d)', niveaux: [3] },
          { key: 'penetration_eau', label: 'Il n\'y a aucun signe visible de pénétration d\'eau ou de poussière dans l\'enveloppe conformément au degré IP (d)', niveaux: [3] },
         {
      key: 'dimensions_espaces',
      label: (
        <>
          Les dimensions des espaces entre joints à brides sont : <br />
          - dans les limites conformément à la documentation du constructeur, ou <br />
          - dans les limites des valeurs maximales autorisées par la norme de construction applicable au moment de l’installation, ou <br />
          - dans les limites des valeurs maximales autorisées par la documentation du site <strong>(d)</strong>
        </>
      ),
      niveaux: [3]
    },
          { key: 'connexions_serrees', label: 'Les connexions électriques sont serrées (e,n,t/tD)', niveaux: [3] },
          { key: 'bornes_non_utilisees', label: 'Les bornes non utilisées sont serrées (e,n)', niveaux: [3] },
          { key: 'dispositifs_scellement', label: 'Les dispositifs enfermés et de scellement hermétique ne sont pas endommagés (n)', niveaux: [3] },
          { key: 'composants_encapsules', label: 'Les composants encapsulés ne sont pas endommagés (e,n)', niveaux: [3] },
          { key: 'composants_ignifuges', label: 'Les composants ignifuges ne sont pas endommagés (e,n)', niveaux: [3] },
          { key: 'enveloppes_respiration', label: 'Les enveloppes à respiration limitée sont satisfaisantes (mode "nR" uniquement) (n)', niveaux: [3] },
          { key: 'port_essai', label: 'Le port d\'essai, s\'il est installé, est fonctionnel (mode "nR" uniquement) (n)', niveaux: [3] },
          { key: 'operation_respiration', label: 'L\'opération de respiration est satisfaisante (mode "nR" uniquement) (d,e,n)', niveaux: [3] },
        ]
      },
      {
        subtitle: 'MATÉRIEL PARTICULIER (ÉCLAIRAGE)',
        points: [
          { key: 'lampes_fluo', label: 'Les lampes fluorescentes n\'indiquent pas d\'effets EOL (e,n,t,td)', niveaux: [] },
          { key: 'lampes_dhi', label: 'Les lampes à décharge à haute intensité - DHI n\'indiquent pas d\'effets EOL', niveaux: [] },
          { key: 'type_lampes', label: 'Le type, les caractéristiques assignées, la configuration des broches et la position des lampes sont corrects', niveaux: [3] },
        ]
      },
      {
        subtitle: 'MATÉRIEL PARTICULIER (MOTEURS)',
        points: [
          { key: 'distance_ventilateur', label: 'La distance entre le ventilateur et l\'enveloppe et/ou les couvercles est suffisante, les systèmes de refroidissement ne sont pas endommagés, les assises de moteur ne présentent aucune indentation ou fissure, etc.', niveaux: [] },
          { key: 'circulation_air', label: 'Aucun obstacle à la circulation de l\'air de ventilation', niveaux: [3] },
          { key: 'resistance_isolement', label: 'La résistance d\'isolement (IR) des enroulements du moteur est satisfaisante', niveaux: [3] },
        ]
      },
      {
        subtitle: 'INSTALLATION - GÉNÉRALITÉS',
        points: [
          { key: 'dommage_cables', label: 'Il n\'y a pas de dommage apparent aux câbles', niveaux: [] },
          { key: 'obturation_traversees', label: 'L\'obturation des travées, des conduites, des tubes et/ou des conduits est satisfaisante', niveaux: [] },
          {
      key: 'liaisons_terre',
      label: (
        <>
          Les liaisons à la terre, y compris toute liaison à la terre supplémentaire,
          sont satisfaisantes (par exemple, les connexions sont serrées et les
          conducteurs ont une section suffisante) : <br />
          – inspection physique <br />
          – inspection visuelle
        </>
      ),
      niveaux: []
    },
          {key: 'engorgements_joints', label: 'Les engorgements adjacents aux joints à brides ignifuges sont conformes aux exigences de la CEI 60079-14 (d)',niveaux: []},
          { key: 'installation_variable', label: 'L\'installation à tension/fréquence variables est conforme à la documentation', niveaux: [] },
          { key: 'type_cable', label: 'Le type de câble est approprié', niveaux: [2] },
          { key: 'boitiers_arret', label: 'Les boîtiers d\'arrêt et les boîtiers de câbles sont correctement remplis', niveaux: [3] },
          { key: 'integrite_conduits', label: 'L\'intégrité des systèmes de conduits et l\'interface avec les systèmes mixtes sont maintenues', niveaux: [3] },
          { key: 'impedance_boucle', label: 'L\'impédance de boucle du défaut (schémas TN) ou la résistance à la terre (schémas IT) est satisfaisante', niveaux: [3] },
          { key: 'protection_electrique', label: 'Les dispositifs automatiques de protection électriques sont correctement réglés (le réarmement automatique n\'est pas possible)', niveaux: [3] },
          {key: 'protection_electrique_chauffage',label: 'Les dispositifs automatiques de protection électriques fonctionnent dans les limites autorisées dans la CEI 60079-14 (d,e)',niveaux: [3]},
          { key: 'conditions_utilisation', label: 'Les conditions particulières d\'utilisation (s\'il y a lieu) sont respectées', niveaux: [3] },
          { key: 'extremites_cables', label: 'Les extrémités de câbles qui ne sont pas en service sont correctement protégées', niveaux: [3] },
        ]
      },
      {
        subtitle: 'INSTALLATION - SYSTÈMES DE CHAUFFAGE',
        points: [
          { key: 'reglage_coupure', label: 'Le réglage de la coupure de sécurité est scellé (d,e)', niveaux: [2,3] },
          { key: 'reinitialisation_coupure', label: 'La réinitialisation d\'une coupure de sécurité du système de chauffage n\'est possible qu\'avec un outil (d,e)', niveaux: [2,3] },
          { key: 'reinitialisation_automatique', label: 'La réinitialisation automatique n\'est pas possible (d,e)', niveaux: [2,3] },
          { key: 'capteurs_temperature', label: 'Les capteurs de température fonctionnent conformément à la documentation du constructeur (t)', niveaux: [3] },
          { key: 'dispositifs_coupure', label: 'Les dispositifs de coupure de sécurité fonctionnent conformément à la documentation du constructeur (t)', niveaux: [3] },
          { key: 'reinitialisation_defaut', label: 'La réinitialisation d\'une coupure de sécurité dans des conditions de défaut est empêchée (d,e)', niveaux: [3] },
          { key: 'coupure_independante', label: 'La coupure de sécurité est indépendante du système de commande (d,e)', niveaux: [3] },
          { key: 'capteur_niveau', label: 'Le capteur de niveau est installé et correctement réglé, si nécessaire (d,e)', niveaux: [3] },
          { key: 'capteur_debit', label: 'Le capteur de débit est installé et correctement réglé, si nécessaire (d,e)', niveaux: [3] },
        ]
      },


{
  subtitle: 'MOTEURS',
  points: [
    {
      key: 'protection_moteur',
      label: 'Les dispositifs de protection du moteur fonctionnent dans les limites de temps autorisées tE ou iA (e)',
      niveaux: [3]
    }
  ]
}
,
      {
        subtitle: 'ENVIRONNEMENT',
        points: [
          { key: 'protection_materiel', label: 'Le matériel est protégé de façon adéquate contre la corrosion, les intempéries, les vibrations et autres facteurs nuisibles', niveaux: [] },
          { key: 'accumulation_poussiere', label: 'Il n\'y a pas d\'accumulation anormale de poussière et de saleté', niveaux: [] },
          { key: 'isolation_electrique', label: 'L\'isolation électrique est propre et sèche (e,t/tD)', niveaux: [3] },
        ]
      }
    ]



    

  },

  {
  title: 'Les installations Ex "i" (Tableau 02 l’IEC 60079-17V2023)',
  subsections: [
    {
      subtitle: 'MATERIEL',
      points: [
        { key: 'doc_materiel_appropriate', label: 'La documentation du matériel et/ou du circuit est appropriée au niveau de protection / exigences de la zone concernée', niveaux: [] },
        { key: 'materiel_specifie', label: 'Le matériel installé est celui qui est spécifié dans la documentation', niveaux: [2,3] },
        { key: 'categorie_groupe_correct', label: 'La catégorie et le groupe du circuit et/ou du matériel sont corrects', niveaux: [2,3] },
        { key: 'degre_ip_groupe3', label: 'Le degré IP du matériel est approprié au matériau de groupe III présent', niveaux: [2,3] },
        { key: 'classe_temp_materiel', label: 'La classe de température du matériel est correcte', niveaux: [2,3] },
        { key: 'plage_temp_ambiante', label: 'La plage de températures ambiantes de l’appareil est correcte pour l’installation', niveaux: [2,3] },
 { key: 'plage_temp_service', label: 'La plage de températures de service de l’appareil est correcte pour l’installation', niveaux: [2,3] },
       
        { key: 'installation_reperee', label: 'L’installation est clairement repérée', niveaux: [2,3] },
       {
  key: 'presse_etoupes_type',
  label: (
    <>
      Les presse-étoupes et éléments d’obturation sont du type correct et sont complets et serrés <br />
      – vérification physique <br />
      – vérification visuelle
    </>
  ),
  niveaux: []
},
        
        { key: 'modification_non_autorisee_i', label: 'Il n’y a pas de modification non autorisée visible', niveaux: [1,2] },
        { key: 'barrieres_securite', label: 'Les barrières de sécurité à diodes, les isolateurs galvaniques, les relais et autres dispositifs de limitation de l’énergie sont d’un type approuvé, sont installés conformément aux exigences de certification et sont convenablement mis à la terre si cela est exigé', niveaux: [] },
        { key: 'tension_max_um', label: 'La tension maximale Um de l’appareil associé n’est pas dépassée', niveaux: [2,3] },
        { key: 'enveloppe_verre_i', label: 'L’enveloppe, les parties en verre et les garnitures et/ou les matériaux d’étanchéité verre sur métal sont satisfaisants', niveaux: [3] },
        { key: 'pas_modif_non_autorisee', label: 'Il n’y a pas de modification non autorisée', niveaux: [3] },
        { key: 'etat_garnitures_env', label: 'L’état des garnitures des enveloppes est satisfaisant', niveaux: [3] },
        { key: 'connexions_serrees_i', label: 'Les connexions électriques sont serrées', niveaux: [3] },
        { key: 'cartes_propres', label: 'Les cartes imprimées sont propres et non endommagées', niveaux: [3] }
      ]
    },
    {
      subtitle: 'INSTALLATION',
      points: [
        { key: 'dommage_cables_i', label: 'Il n’y a pas de dommage apparent aux câbles', niveaux: [] },
        { key: 'obturation_traversees_i', label: 'L’obturation des travées, des conduites, des tubes et/ou des conduits est satisfaisante', niveaux: [] },
        { key: 'cables_documentation', label: 'Les câbles sont installés conformément à la documentation', niveaux: [3] },
        { key: 'ecrans_documentation', label: 'Les écrans des câbles sont mis à la terre conformément à la documentation', niveaux: [3] },
        { key: 'connexions_point_point', label: 'Les connexions point à point sont toutes correctes (inspection initiale uniquement)', niveaux: [3] },
        { key: 'continuite_terre', label: 'La continuité des liaisons à la terre est satisfaisante (par exemple, les connexions sont serrées, les conducteurs ont une section suffisante) pour les circuits non isolés galvaniquement', niveaux: [3] },
        { key: 'liaison_terre_integrite', label: 'Les liaisons à la terre n’affectent pas l’intégrité du mode de protection', niveaux: [3] },
        { key: 'mise_a_la_terre_sic', label: 'La mise à la terre du circuit de sécurité intrinsèque est satisfaisante', niveaux: [3] },
        { key: 'resistance_isolement_i', label: 'La résistance d\'isolement est satisfaisante', niveaux: [3] },
        { key: 'separation_circuits', label: 'La séparation entre les circuits de sécurité intrinsèque et les circuits de sécurité non intrinsèque est assurée lorsque ces circuits sont dans un même boîtier de distribution ou dans un même boîtier relais', niveaux: [3] },
        { key: 'protection_cc_alim', label: 'La protection contre les courts-circuits de l’alimentation est conforme à la documentation', niveaux: [3] },
        { key: 'conditions_utilisation_i', label: 'Les conditions particulières d’utilisation (s’il y a lieu) sont respectées', niveaux: [3] },
        { key: 'extremites_cables_prot', label: 'Les extrémités de câbles qui ne sont pas en service sont correctement protégées', niveaux: [3] }
      ]
    },
    {
      subtitle: 'ENVIRONNEMENT',
      points: [
        { key: 'protection_materiel_i', label: 'Le matériel est protégé de façon adéquate contre la corrosion, les intempéries, les vibrations et autres facteurs nuisibles', niveaux: [] },
        { key: 'accumulation_poussiere_ext', label: 'Il n’y a pas d’accumulation anormale extérieure de poussière et de saleté', niveaux: [] }
      ]
    }
  ]
},

{
  title: 'Les installations Ex "o" (Tableau 04 l’IEC 60079-17V2023)',
  subsections: [
    {
      subtitle: 'ÉQUIPMENT',
      points: [
        { key: 'mat_approprie_o', label: 'Le matériel est approprié aux exigences relatives au niveau de protection/zone de l’emplacement concerné', niveaux: [] },
        { key: 'groupe_correct_o', label: 'Le groupe de matériel est correct', niveaux: [2,3] },
        { key: 'classe_temp_o', label: 'La classe de température du matériel est correcte', niveaux: [2,3] },
        { key: 'identification_circuit_o', label: 'L’identification du circuit du matériel est disponible', niveaux: [] },
        { key: 'enveloppe_verre_o', label: 'L’enveloppe, les parties en verre et les garnitures et/ou les matériaux d’étanchéité verre sur métal sont satisfaisants', niveaux: [] },
        { key: 'modification_non_autorisee_o', label: 'Il n’y a pas de modification non autorisée visible', niveaux: [1,2] },
        { key: 'boulons_entrees_o', label: (
          <>
            Les boulons, les dispositifs d’entrées de câbles (directes et indirectes) et les éléments d\'obturation sont d’un type correct et sont complets et serrés <br />
            – vérification physique <br />
            – vérification visuelle
          </>
        ), niveaux: [] },
        { key: 'enveloppes_scellees_o', label: 'Les enveloppes portant la mention « Scellée de façon permanente » ne présentent aucun signe visible d’ouverture.', niveaux: [] },
        { key: 'critere_liquide_protection', label: (
          <>
            Critères maximum/minimum du liquide de protection <br />
            a) Le niveau du liquide de protection doit être inférieur ou égal au niveau maximum autorisé et supérieur au niveau minimum autorisé ; <br />
            b) L’angle de fonctionnement maximum par rapport à l’horizontale de l’équipement doit être conforme. Lorsque qu'une jauge est fournie, celle-ci doit être fixée en position de mesure et son étanchéité doit être satisfaisante.
          </>
        ), niveaux: [] },
        { key: 'fonctionnement_tele_signalisation', label: 'Le fonctionnement du dispositif de télésignalisation du niveau du liquide de protection est satisfaisant.', niveaux: [] },
        { key: 'programme_nettoyage_liquide', label: 'Le programme de nettoyage, de filtration ou de remplacement du liquide de protection du dispositif de commutation, après un nombre donné de manœuvres normales ou d’interruptions de courants de défaut, est documenté.', niveaux: [] },
        { key: 'identification_circuits_o', label: 'L’identification des circuits de l’équipement est correcte.', niveaux: [3] },
        { key: 'pas_modif_non_autorisee_o', label: 'Aucune modification non autorisée n’a été constatée.', niveaux: [3] },
        { key: 'connexions_serrees_o', label: 'Les connexions électriques sont bien serrées.', niveaux: [3] },
        { key: 'etat_joints_etancheite', label: 'L\'état des joints d\'étanchéité de l’enveloppe est satisfaisant.', niveaux: [3] },
        { key: 'dispositifs_respiration_o', label: 'Les dispositifs de respiration et de drainage sont satisfaisants. Le programme d’entretien du fabricant concernant les exigences liées à l’agent de séchage a été respecté et dûment documenté.', niveaux: [3] },
        { key: 'surpression_enveloppe', label: 'Les dispositifs de surpression des enveloppes scellées sont en bon état de fonctionnement.', niveaux: [3] },
        { key: 'niveau_liquide_o', label: 'Pour les enveloppes destinées à être ouvertes, le niveau du liquide de protection dans le cadre du mode de protection de type “o” est correct.', niveaux: [3] }
      ]
    },
    {
      subtitle: 'INSTALLATION - GÉNÉRALITÉS',
      points: [
        { key: 'dommage_cables_o', label: 'Il n’y a pas de dommage apparent aux câbles', niveaux: [] },
        { key: 'obturation_traversees_o', label: 'L’obturation des travées, des conduites, des tubes et/ou des conduits est satisfaisante', niveaux: [] },
        { key: 'liaisons_terre_o', label: (
          <>
            Les liaisons à la terre, y compris toute liaison à la terre supplémentaire, sont satisfaisantes (par exemple, les connexions sont serrées et les conducteurs ont une section suffisante) <br />
            – inspection physique <br />
            – inspection visuelle
          </>
        ), niveaux: [] },
        { key: 'installation_variable_o', label: 'L’installation à tension/fréquence variables est conforme à la documentation', niveaux: [] },
        { key: 'type_cable_o', label: 'Le type de câble est approprié', niveaux: [3] },
        { key: 'boitiers_coupe_feu', label: 'Les dispositifs coupe-feu, les boites de jonction et les entrées de câbles sont correctement remplis', niveaux: [3] },
        { key: 'integrite_conduits_o', label: 'L’intégrité des systèmes de conduits et l’interface avec les systèmes mixtes sont maintenues', niveaux: [3] },
        { key: 'impedance_boucle_o', label: 'L’impédance de boucle du défaut (schémas TN) ou la résistance à la terre (schémas IT) est satisfaisante', niveaux: [3] },
        { key: 'protection_electrique_o', label: 'Les dispositifs automatiques de protection électriques sont correctement réglés (le réarmement automatique n’est pas possible)', niveaux: [3] },
        { key: 'protection_electrique_autorisee', label: 'Les dispositifs automatiques de protection électriques fonctionnent dans les limites autorisées', niveaux: [3] },
        { key: 'conditions_utilisation_o', label: 'Les conditions particulières d’utilisation (s’il y a lieu) sont respectées', niveaux: [3] },
        { key: 'extremites_cables_prot_o', label: 'Les extrémités de câbles qui ne sont pas en service sont correctement protégées.', niveaux: [3] }
      ]
    },
    {
      subtitle: 'INSTALLATION – SYSTÈMES DE CHAUFFAGE',
      points: [
        { key: 'reglage_coupure_o', label: 'Le réglage de la coupure de sécurité est scellé. ', niveaux: [] },
        {
      key: 'reinitialisation_coupure_outil',
      label: 'La réinitialisation d’une coupure de sécurité du système de chauffage n’est possible qu’avec un outil',
      niveaux: []
    },
        { key: 'reinitialisation_auto_o', label: 'La réinitialisation automatique n’est pas possible', niveaux: [] },
        { key: 'capteurs_temperature_o', label: 'Les capteurs de température fonctionnent conformément à la documentation du constructeur', niveaux: [3] },
        { key: 'dispositifs_coupure_o', label: 'Les dispositifs de coupure de sécurité fonctionnent conformément à la documentation du constructeur', niveaux: [3] },
        { key: 'reinitialisation_defaut_o', label: 'La réinitialisation d’une coupure de sécurité dans des conditions de défaut est empêchée', niveaux: [3] },
        { key: 'coupure_independante_o', label: 'La coupure de sécurité est indépendante du système de commande', niveaux: [3] },
        { key: 'capteur_niveau_o', label: 'Le capteur de niveau est installé et correctement réglé, si nécessaire', niveaux: [3] },
        { key: 'capteur_debit_o', label: 'Le capteur de débit est installé et correctement réglé, si nécessaire', niveaux: [3] }
      ]
    },
    {
      subtitle: 'ENVIRONNEMENT',
      points: [
        { key: 'protection_materiel_o', label: 'Le matériel est protégé de façon adéquate contre la corrosion, les intempéries, les vibrations et autres facteurs nuisibles', niveaux: [] },
        { key: 'accumulation_poussiere_o', label: 'Il n’y a pas d’accumulation anormale de poussière et de saleté', niveaux: [] },
        { key: 'isolation_electrique_o', label: 'L’isolation électrique est propre et sèche', niveaux: [3] }
      ]
    }
  ]
},

{
  title: 'Les installations Ex "p" et "pD" (Tableau 03 l’IEC 60079-17V2023)',
  subsections: [
    {
      subtitle: 'MATERIEL',
      points: [
        { key: 'mat_approprie_p', label: 'Le matériel est approprié au niveau de protection/aux exigences de la zone concernée', niveaux: [] },
        { key: 'groupe_correct_p', label: 'Le groupe de matériel est correct', niveaux: [2,3] },
        { key: 'classe_temp_p', label: 'La classe de température du matériel ou la température de surface est correcte', niveaux: [2,3] },
        { key: 'identification_circuit_p', label: 'L’identification du circuit du matériel est disponible', niveaux: [] },
        { key: 'enveloppe_verre_p', label: 'L’enveloppe, les parties en verre et les garnitures et/ou matériaux d’étanchéité verre sur métal sont satisfaisants', niveaux: [] },
        { key: 'modification_non_autorisee_p', label: 'Il n’y a pas de modification non autorisée visible', niveaux: [1,2] },
        { key: 'identification_circuit_correcte_p', label: 'L’identification du circuit du matériel est correcte', niveaux: [3] },
        { key: 'pas_modif_non_autorisee_p', label: 'Il n’y a pas de modification non autorisée', niveaux: [3] },
        { key: 'type_lampes_p', label: 'Le type, les caractéristiques assignées et la position des lampes sont corrects', niveaux: [3] }
      ]
    },
    {
      subtitle: 'INSTALLATION',
      points: [
        { key: 'dommage_cables_p', label: 'Il n’y a pas de dommage apparent aux câbles', niveaux: [] },
        { key: 'liaisons_terre_p', label: (
          <>
            Les liaisons à la terre, y compris toute liaison à la terre supplémentaire, sont satisfaisantes, par exemple les connexions sont serrées et les conducteurs ont une section suffisante <br />
            – vérification physique <br />
            – vérification visuelle
          </>
        ), niveaux: [] },
        { key: 'conduites_tubes_p', label: 'Les conduites, tubes et enveloppes sont en bon état', niveaux: [] },
        { key: 'gaz_impuretes_p', label: 'Le gaz de protection ne contient pas d\'impuretés', niveaux: [] },
        { key: 'pression_debit_gaz', label: 'La pression et/ou le débit du gaz de protection sont appropriés', niveaux: [] },
        { key: 'type_cable_p', label: 'Le type de câble est approprié', niveaux: [3] },
        {
  key: 'impedance_boucle_it',
  label: (
    <>
      L’impédance de boucle du défaut (schémas TN) ou la résistance à la terre <strong>(schémas IT)</strong> est satisfaisante
    </>
  ),
  niveaux: [3]
},
        { key: 'protection_electrique_p', label: 'Les dispositifs automatiques de protection électriques fonctionnent dans les limites autorisées', niveaux: [3] },
        { key: 'protection_reglee_p', label: 'Les dispositifs automatiques de protection électriques sont correctement réglés fonctionnent cirrectement', niveaux: [3] },
        { key: 'temp_entree_gaz', label: 'La température d’entrée du gaz de protection est inférieure au maximum spécifié', niveaux: [3] },
        { key: 'barrieres_etincelles', label: 'Les conditions des barrières contre les étincelles et les particules des conduits pour évacuer le gaz des emplacements dangereux sont satisfaisantes', niveaux: [3] },
        { key: 'conditions_utilisation_p', label: 'Les conditions particulières d’utilisation (s’il y a lieu) sont respectées', niveaux: [3] }
      ]
    },
    {
      subtitle: 'ENVIRONNEMENT',
      points: [
        { key: 'protection_materiel_p', label: 'Le matériel est protégé de façon adéquate contre la corrosion, les intempéries, les vibrations et autres facteurs nuisibles', niveaux: [] },
        { key: 'accumulation_poussiere_p', label: 'Il n’y a pas d’accumulation anormale de poussière et de saleté', niveaux: [] }
      ]
    }
  ]
}

];

const InspectionFormPage = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // State for companies
  const [companies, setCompanies] = useState([]);

  // Check if we're editing an existing inspection
  const editData = location.state?.editData;
  const isEdit = location.state?.isEdit;

  // Fetch companies on component mount
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await api.get('/api/inspections/companies/');
        setCompanies(response.data);
      } catch (error) {
        console.error('Error fetching companies:', error);
      }
    };

    fetchCompanies();
  }, []);

  // Helper function to extract text from label (handles both string and JSX)
  const extractLabelText = (label) => {
    if (typeof label === 'string') {
      return label;
    }
    if (label && label.props && label.props.children) {
      if (Array.isArray(label.props.children)) {
        return label.props.children.map(child =>
          typeof child === 'string' ? child : (child.props?.children || '')
        ).join(' ');
      }
      return typeof label.props.children === 'string' ? label.props.children : '';
    }
    return '';
  };

  // Helper function to check if a point applies to the selected mode
  const pointAppliesToMode = (point, modeProtection) => {
    if (!modeProtection) return false;

    const labelText = extractLabelText(point.label);

    // Look for mode indicators in parentheses like (d), (e), (n), etc.
    const modePattern = /\(([^)]*)\)/gi;
    const matches = labelText.match(modePattern);

    if (!matches || matches.length === 0) {
      // If no mode indicators found, show the point (general points)
      return true;
    }

    // Check if any match contains our mode
    return matches.some(match => {
      const modesInParens = match.replace(/[()]/g, '').toLowerCase();
      // Split by common separators and check each mode
      const modes = modesInParens.split(/[,\/\s]+/).map(m => m.trim()).filter(m => m);

      // Handle special cases
      const currentMode = modeProtection.toLowerCase();

      // Check for exact match
      if (modes.includes(currentMode)) return true;

      // Handle tD case (can be written as "td" or "t/td")
      if (currentMode === 'td' && (modes.includes('td') || modes.includes('t/td'))) return true;

      // Handle t case when t/tD is present
      if (currentMode === 't' && modes.some(m => m.includes('t/td') || m.includes('t,td'))) return true;

      return false;
    });
  };

  // Prepare initial values - use edit data if available, otherwise use defaults
  const getInitialValues = () => {
    if (editData) {
      console.log('EDIT MODE: Processing edit data:', editData);
      // Create a copy of the edit data but clear the ID and update fiche_num
      const editValues = { ...editData };
      delete editValues.id; // Remove ID so a new record is created
      delete editValues.created_at;
      delete editValues.created_by;
      delete editValues.pdf_file;

      // Remove photo fields to prevent them from being included as empty strings
      delete editValues.photo_marquage;
      delete editValues.photo_equipement;
      delete editValues.photos_anomalies;

      // Generate COPY_ format for edited forms
      const currentDate = new Date();
      const dateStr = currentDate.toISOString().split('T')[0].replace(/-/g, '');
      const timeStr = currentDate.toTimeString().split(' ')[0].replace(/:/g, '');
      editValues.fiche_num = `COPY_${dateStr}_${timeStr}`;
      editValues.date = currentDate.toISOString().split('T')[0];

      // Keep the original generation method (don't force manual)
      // This allows users to choose between manual (editable) and automatic (COPY_ format)
      editValues.fiche_generation_method = editValues.fiche_generation_method || 'manual';

      console.log('EDIT MODE: Generated COPY format:', {
        original_fiche_num: editData.fiche_num,
        original_method: editData.fiche_generation_method,
        new_fiche_num: editValues.fiche_num,
        new_method: editValues.fiche_generation_method,
        date: editValues.date
      });

      return editValues;
    }
    console.log('NEW FORM: Using initial values');
    return initialValues;
  };

  const onSubmit = async (values, retryCount = 0) => {
    const MAX_RETRIES = 2;
    try {
      console.log('Submitting values:', values, retryCount > 0 ? `(Retry ${retryCount}/${MAX_RETRIES})` : '');
      console.log('DEBUG: Online status:', isOnline());

      // Check if we're online
      if (!isOnline()) {
        console.log('DEBUG: User is offline, storing locally');
        alert('Vous êtes hors ligne. L\'inspection sera sauvegardée localement et synchronisée automatiquement lorsque la connexion sera rétablie.');

        // Store the form data locally for later submission
        const pendingInspection = {
          ...values,
          submittedAt: new Date().toISOString(),
          status: 'pending_sync'
        };

        // Save to local storage
        try {
          const existingPending = JSON.parse(localStorage.getItem('pendingInspections') || '[]');
          existingPending.push(pendingInspection);
          localStorage.setItem('pendingInspections', JSON.stringify(existingPending));
          console.log('DEBUG: Inspection saved locally successfully');

          alert('Inspection sauvegardée localement. Elle sera synchronisée automatiquement quand vous serez en ligne.');
        } catch (error) {
          console.error('Error saving inspection locally:', error);
          alert('Erreur lors de la sauvegarde locale. Veuillez réessayer.');
        }

        return;
      }

      // Handle automatic generation differently for edit mode
      if (values.fiche_generation_method === 'automatic') {
        if (!values.projet) {
          alert('Project name is required for automatic fiche number generation.');
          return;
        }
        if (!values.date) {
          alert('Date is required for automatic fiche number generation.');
          return;
        }

        if (isEdit) {
          // For edit mode: generate COPY_ format automatically
          const currentDate = new Date();
          const dateStr = currentDate.toISOString().split('T')[0].replace(/-/g, '');
          const timeStr = currentDate.toTimeString().split(' ')[0].replace(/:/g, '');
          values.fiche_num = `COPY_${dateStr}_${timeStr}`;
          console.log('EDIT MODE: Auto-generated COPY format:', values.fiche_num);
          console.log('EDIT MODE: fiche_generation_method:', values.fiche_generation_method);
        } else {
          // For new forms: let the backend handle sequential generation
          console.log('NEW FORM: Using automatic fiche number generation - backend will generate the number');
        }
      }

      // Store photo files separately before processing
      const photoMarquage = values.photo_marquage;
      const photoEquipement = values.photo_equipement;
      const photosAnomalies = values.photos_anomalies;

      // Debug: Check what's in the photo fields
      console.log('DEBUG: Photo field values:');
      console.log('DEBUG: photoMarquage:', photoMarquage, 'type:', typeof photoMarquage, 'instanceof File:', photoMarquage instanceof File);
      if (photoMarquage instanceof File) {
        console.log('DEBUG: photoMarquage size:', photoMarquage.size, 'bytes, type:', photoMarquage.type);
      }
      console.log('DEBUG: photoEquipement:', photoEquipement, 'type:', typeof photoEquipement, 'instanceof File:', photoEquipement instanceof File);
      if (photoEquipement instanceof File) {
        console.log('DEBUG: photoEquipement size:', photoEquipement.size, 'bytes, type:', photoEquipement.type);
      }
      console.log('DEBUG: photosAnomalies:', photosAnomalies, 'type:', typeof photosAnomalies, 'Array.isArray:', Array.isArray(photosAnomalies));
      if (Array.isArray(photosAnomalies)) {
        console.log('DEBUG: photosAnomalies count:', photosAnomalies.length);
        photosAnomalies.forEach((file, index) => {
          if (file instanceof File) {
            console.log(`DEBUG: photosAnomalies[${index}] size:`, file.size, 'bytes, type:', file.type);
          } else {
            console.log(`DEBUG: photosAnomalies[${index}] is not a File object:`, typeof file);
          }
        });
      }

      // Create FormData for file uploads
      const formData = new FormData();

      // Authentication is handled via token in API interceptor
      // No need to manually send username

      // Define allowed fields (exclude photo fields completely)
      const allowedFields = [
        'fiche_num', 'fiche_generation_method', 'date', 'projet', 'equipement', 'equipement_custom', 'tag',
        'constructeur', 'model', 'numero_serie', 'date_installation', 'age', 'puissance', 'courant', 'tension',
        'unite', 'unite_custom', 'localisation', 'zone_atex', 'groupe_gaz', 'classe_t',
        'marquage_atex_g', 'marquage_atex_d', 'marquage_us', 'type_marquage',
        'mode_protection', 'organisme_notifie', 'ip', 'nema',
        'certificat', 'tamb_min', 'tamb_max', 'atex_oui', 'atex_non',
        'acces_inaccessible', 'acces_calorifuge', 'acces_peinte',
        'acces_inaccessible_plaque', 'acces_illisible',
        'acces_pas_plaque', 'niveau_1', 'niveau_2', 'niveau_3',
        'points', 'observations', 'action',
        'date_precedente_inspection', 'inspecteur', 'qualifications', 'numero_certificat', 'observations_complementaires',
        'inspected_company_name'
      ];

      // Add only allowed fields to FormData (explicitly exclude photo fields)
      allowedFields.forEach(key => {
        if (values.hasOwnProperty(key) && key !== 'photo_marquage' && key !== 'photo_equipement' && key !== 'photos_anomalies') {
          if (typeof values[key] === 'object' && values[key] !== null) {
            formData.append(key, JSON.stringify(values[key]));
          } else {
            formData.append(key, values[key] || '');
          }
        }
      });

      // File size validation (10MB limit per file)
      const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
      const validateFileSize = (file, fileName) => {
        if (file.size > MAX_FILE_SIZE) {
          throw new Error(`File ${fileName} is too large (${(file.size / 1024 / 1024).toFixed(2)}MB). Maximum size is 10MB.`);
        }
      };

      // Calculate total upload size for user feedback
      let totalUploadSize = 0;
      let fileCount = 0;
      if (photoMarquage) {
        totalUploadSize += photoMarquage.size;
        fileCount++;
      }
      if (photoEquipement) {
        totalUploadSize += photoEquipement.size;
        fileCount++;
      }
      if (photosAnomalies) {
        photosAnomalies.forEach(file => {
          totalUploadSize += file.size;
          fileCount++;
        });
      }

      const totalSizeMB = (totalUploadSize / 1024 / 1024).toFixed(2);
      console.log(`DEBUG: Total upload size: ${totalSizeMB}MB (${fileCount} files)`);

      // Check for 413 error prevention - limit total upload size
      const MAX_TOTAL_SIZE = 15 * 1024 * 1024; // 15MB total limit
      if (totalUploadSize > MAX_TOTAL_SIZE) {
        alert(`Total upload size (${totalSizeMB}MB) exceeds the server limit of 15MB. Please reduce file sizes or upload fewer files.`);
        return;
      }

      // Warn user about large uploads
      if (totalUploadSize > 8 * 1024 * 1024) { // 8MB
        const proceed = window.confirm(`You're uploading ${totalSizeMB}MB of files (${fileCount} files). This is a large upload and may take time. Continue?`);
        if (!proceed) {
          return;
        }
      }

      // Only add photo files if they are actual File objects
      if (photoMarquage && photoMarquage instanceof File) {
        validateFileSize(photoMarquage, 'photo_marquage');
        formData.append('photo_marquage', photoMarquage);
        console.log('DEBUG: Added photo_marquage file');
      }

      if (photoEquipement && photoEquipement instanceof File) {
        validateFileSize(photoEquipement, 'photo_equipement');
        formData.append('photo_equipement', photoEquipement);
        console.log('DEBUG: Added photo_equipement file');
      }

      if (photosAnomalies && Array.isArray(photosAnomalies) && photosAnomalies.length > 0) {
        photosAnomalies.forEach((file, index) => {
          if (file instanceof File) {
            validateFileSize(file, `photos_anomalies_${index}`);
            formData.append(`photos_anomalies_${index}`, file);
          }
        });
        formData.append('photos_anomalies_count', photosAnomalies.length);
        console.log('DEBUG: Added photos_anomalies files');
      }

      // Debug: Log what's being sent
      console.log('DEBUG: Original values object:', values);
      console.log('DEBUG: Photo files:', { photoMarquage, photoEquipement, photosAnomalies });
      console.log('DEBUG: FormData keys being sent:');
      for (let [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`DEBUG: ${key}: File (${value.name}, ${value.size} bytes, ${value.type})`);
        } else {
          console.log(`DEBUG: ${key}:`, typeof value === 'string' && value.length > 100 ? value.substring(0, 100) + '...' : value);
        }
      }

      console.log('DEBUG: About to make API call to /api/inspections/inspections/');
      const startTime = Date.now();

      try {
        const response = await api.post('/api/inspections/inspections/', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 300000, // 300 second timeout for file uploads (increased from 120s)
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            const loadedMB = (progressEvent.loaded / 1024 / 1024).toFixed(2);
            const totalMB = (progressEvent.total / 1024 / 1024).toFixed(2);
            console.log(`DEBUG: Upload progress: ${percentCompleted}% (${loadedMB}MB / ${totalMB}MB)`);

            // Show progress to user if possible
            if (percentCompleted % 10 === 0) { // Update every 10%
              console.log(`Upload progress: ${percentCompleted}% complete`);
            }
          },
        });

        const endTime = Date.now();
        console.log('DEBUG: API call successful, took', (endTime - startTime), 'ms');
        console.log('DEBUG: Response status:', response.status);
        console.log('DEBUG: Response data:', response.data);
      } catch (apiError) {
        const endTime = Date.now();
        console.error('DEBUG: API call failed after', (endTime - startTime), 'ms');
        console.error('DEBUG: API Error details:', apiError);
        if (apiError.response) {
          console.error('DEBUG: Response status:', apiError.response.status);
          console.error('DEBUG: Response data:', apiError.response.data);
          console.error('DEBUG: Response headers:', apiError.response.headers);

          // Handle specific error cases
          if (apiError.response.status === 413) {
            const totalSizeMB = (totalUploadSize / 1024 / 1024).toFixed(2);
            alert(`Upload too large (${totalSizeMB}MB). The server limit is 15MB total. Please:\n• Reduce image quality\n• Resize large images\n• Upload fewer files\n• Contact support if needed`);
          } else if (apiError.response.status === 401) {
            alert('Authentication failed. Please log in again.');
            // Redirect to login
            window.location.href = '/login';
          } else if (apiError.response.status === 400) {
            alert('Invalid data submitted. Please check your form and try again.');
          }
        } else if (apiError.request) {
          console.error('DEBUG: No response received, request details:', apiError.request);
          if (apiError.code === 'ECONNABORTED') {
            alert('Upload timed out. This may be due to large file sizes or slow network connection. Try reducing file sizes or check your connection.');
          } else if (apiError.code === 'NETWORK_ERROR') {
            alert('Network error occurred during upload. Please check your internet connection and try again.');
          } else {
            alert('Network error during upload. Please check your connection and try again.');
          }
        } else {
          console.error('DEBUG: Error setting up request:', apiError.message);
          alert('An error occurred while preparing the upload. Please check your files and try again.');
        }

        // Add additional debugging for file upload issues
        console.error('DEBUG: Upload failed with the following file details:');
        if (photoMarquage) console.error('DEBUG: photo_marquage:', photoMarquage.name, photoMarquage.size, 'bytes');
        if (photoEquipement) console.error('DEBUG: photo_equipement:', photoEquipement.name, photoEquipement.size, 'bytes');
        if (photosAnomalies && photosAnomalies.length > 0) {
          console.error('DEBUG: photos_anomalies count:', photosAnomalies.length);
          photosAnomalies.forEach((file, index) => {
            console.error(`DEBUG: photos_anomalies[${index}]:`, file.name, file.size, 'bytes');
          });
        }
        throw apiError; // Re-throw to be caught by outer catch
      }

      // Check if the response contains a valid ID
      if (!response.data || !response.data.id) {
        throw new Error('Invalid response from server - no inspection ID received');
      }

      // Get the inspection ID from the response
      const inspectionId = response.data.id;
      console.log('Inspection created with ID:', inspectionId);

      // Fetch the PDF file
      const pdfResponse = await api.get(`/api/inspections/inspections/${inspectionId}/generate_pdf/`, {
        responseType: 'blob'  // Important: This tells axios to handle the response as binary data
      });
      
      // Create a blob URL from the PDF data
      const blob = new Blob([pdfResponse.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      
      // Create a temporary link element and trigger the download
      const link = document.createElement('a');
      link.href = url;
      link.download = `inspection_${values.fiche_num}_${values.date}.pdf`;
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      if (isEdit) {
        alert('New inspection created successfully based on the original!');
        navigate('/documents', { state: { refresh: true } }); // Navigate back to documents page with refresh flag
      } else {
        alert('Inspection submitted successfully and PDF downloaded!');
      }
    } catch (error) {
      console.error('Error submitting inspection:', error, retryCount > 0 ? `(Retry ${retryCount}/${MAX_RETRIES})` : '');

      // Check if we should retry for network-related errors
      const shouldRetry = retryCount < MAX_RETRIES && (
        !error.response || // Network error (no response)
        error.code === 'ECONNABORTED' || // Timeout
        error.code === 'NETWORK_ERROR' || // Network error
        error.response?.status >= 500 // Server errors
      );

      if (shouldRetry) {
        console.log(`DEBUG: Retrying submission in 3 seconds... (${retryCount + 1}/${MAX_RETRIES})`);
        alert(`Upload failed. Retrying in 3 seconds... (${retryCount + 1}/${MAX_RETRIES})`);

        // Wait 3 seconds before retrying
        await new Promise(resolve => setTimeout(resolve, 3000));
        return onSubmit(values, retryCount + 1);
      }

      // Final failure - show error message
      if (error.response) {
        console.error('Error response:', error.response.data);
        if (error.response.status === 413) {
          alert('Files are too large. Please reduce file sizes and try again.');
        } else if (error.response.status === 401) {
          alert('Authentication failed. Please log in again.');
          window.location.href = '/login';
        } else if (error.response.status === 400) {
          alert('Invalid data submitted. Please check your form and try again.');
        } else {
          alert(`Server error (${error.response.status}). Please try again later.`);
        }
      } else {
        alert('Network error. Please check your connection and try again.');
      }
    }
  };

  return (
    <Box sx={{
      marginLeft: '280px',
      width: 'calc(100% - 280px)',
      padding: '0',
      backgroundColor: '#f9f9fc',
      minHeight: 'calc(100vh - 64px)',
      marginTop: '-680px',
      position: 'relative',
    }}>
      <Container
        maxWidth={false} // Remove max width constraint for large screens
        sx={{
          paddingTop: { xs: 1, sm: 2 },
          paddingBottom: 2,
          px: { xs: 0.5, sm: 1, md: 2, lg: 3, xl: 4 }, // More padding for larger screens
          minHeight: '100vh', // Ensure full height
          width: '100%',
          maxWidth: { xs: '100%', sm: '100%', md: '100%', lg: '1400px', xl: '1600px' } // Custom max widths
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: { xs: 1, sm: 2, md: 3, lg: 4, xl: 5 }, // More padding for larger screens
            backgroundColor: 'white',
            overflow: 'visible', // Allow content to be visible
            width: '100%',
            boxSizing: 'border-box',
            minHeight: 'auto',
            position: 'relative' // Ensure proper positioning
          }}
        >
          <Typography
            variant="h4"
            align="center"
            gutterBottom
            sx={{
              fontWeight: { xs: 600, sm: 'bold', lg: 'bold', xl: 'bold' }, // Bold on larger screens
              mb: { xs: 1.5, sm: 2, md: 3 }, // Progressive margin
              fontSize: {
                xs: '1rem',    // Very small for mobile portrait
                sm: '1.3rem',  // Small tablets
                md: '1.8rem',  // Medium screens (laptops)
                lg: '2.2rem',  // Large screens (1920x1080)
                xl: '2.5rem'   // Extra large screens (4K)
              },
              lineHeight: {
                xs: 1.1,      // Very tight line height on mobile
                sm: 1.2,
                md: 1.3,
                lg: 1.4,      // Good for large screens
                xl: 1.5       // More spacious for 4K
              },
              px: { xs: 0.25, sm: 0.5, md: 1, lg: 2, xl: 3 }, // Very responsive padding
              mx: { xs: 0.25, sm: 0.5, md: 1, lg: 2 },         // Responsive margin
              wordBreak: 'break-word',                          // Allow word breaking
              hyphens: 'auto',                                  // Enable hyphenation
              overflowWrap: 'break-word',                       // Additional word wrapping
              textAlign: 'center',                              // Ensure center alignment
              whiteSpace: 'normal',                             // Allow text wrapping
              width: '100%',                                    // Full width
              maxWidth: 'none',                                 // Remove max width constraint
              display: 'block',                                 // Ensure block display
              position: 'relative',                             // Ensure proper positioning
              zIndex: 1,                                        // Ensure it's on top
              visibility: 'visible',                            // Explicitly set visibility
              overflow: 'visible',                              // Ensure text isn't clipped
              // Additional mobile-specific styles
              '@media (max-width: 600px)': {
                fontSize: '0.9rem',
                lineHeight: 1.05,
                fontWeight: 500,
                letterSpacing: '-0.02em'
              },
              // Extra small screens (portrait phones)
              '@media (max-width: 400px)': {
                fontSize: '0.8rem',
                lineHeight: 1.0,
                fontWeight: 500,
                letterSpacing: '-0.03em',
                px: 0.1
              },
              // Large desktop screens (1920x1080 and above)
              '@media (min-width: 1200px)': {
                fontSize: '2.3rem',
                lineHeight: 1.4,
                fontWeight: 'bold',
                letterSpacing: '0.01em',
                width: '100%',
                maxWidth: 'none',
                overflow: 'visible',
                whiteSpace: 'nowrap', // Prevent wrapping on large screens
                textOverflow: 'visible'
              },
              // Extra large screens (4K and above)
              '@media (min-width: 1920px)': {
                fontSize: '2.6rem',
                lineHeight: 1.5,
                fontWeight: 'bold',
                letterSpacing: '0.02em',
                width: '100%',
                maxWidth: 'none',
                overflow: 'visible',
                whiteSpace: 'nowrap', // Prevent wrapping on 4K screens
                textOverflow: 'visible'
              }
            }}
          >
            {isEdit ? 'CRÉER UNE NOUVELLE INSPECTION (BASÉE SUR L\'EXISTANTE)' : 'INSPECTION VISUELLE & DE PRÈS'}
          </Typography>
          {isEdit && (
            <Typography variant="body1" align="center" sx={{ mb: 3, color: '#1976d2', fontStyle: 'italic' }}>
              Vous créez une nouvelle inspection basée sur l'inspection existante. Les données ont été pré-remplies pour votre commodité.
            </Typography>
          )}
          <Formik
            initialValues={getInitialValues()}
            enableReinitialize={true}
            onSubmit={onSubmit}
          >
            {({ values, handleChange, setFieldValue, isSubmitting }) => (
              <Form>
                {/* En-tête */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2 }}>
                  <Grid container spacing={2} sx={{ p: 2, background: '#f5f5f5' }}>
                    {/* Fiche Generation Method Selection */}
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                        Méthode de génération du numéro de fiche
                        {isEdit && (
                          <Typography variant="caption" sx={{ ml: 1, color: 'text.secondary', fontStyle: 'italic' }}>
                            (Les deux modes génèrent le format COPY_ pour les copies)
                          </Typography>
                        )}
                      </Typography>
                      <FormControl component="fieldset">
                        <RadioGroup
                          row
                          name="fiche_generation_method"
                          value={values.fiche_generation_method}
                          onChange={handleChange}
                        >
                          <FormControlLabel
                            value="manual"
                            control={<Radio />}
                            label="Saisie manuelle (modifiable)"
                          />
                          <FormControlLabel
                            value="automatic"
                            control={<Radio />}
                            label="Génération automatique"
                          />
                        </RadioGroup>
                      </FormControl>
                    </Grid>

                    {/* Conditional Fiche Number Field */}
                    {values.fiche_generation_method === 'manual' ? (
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Fiche n°"
                          name="fiche_num"
                          value={values.fiche_num}
                          onChange={handleChange}
                        />
                      </Grid>
                    ) : (
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Fiche n° (généré automatiquement)"
                          name="fiche_num"
                          value={values.fiche_num || `${values.projet ? 'Auto' : '?'}/${values.projet || 'Projet'}/${values.date || 'Date'}`}
                          disabled
                          helperText="Format: numéro séquentiel/projet/date"
                        />
                      </Grid>
                    )}

                    <Grid item xs={12} sm={4}><TextField fullWidth label="Date" name="date" type="date" slotProps={{ inputLabel: { shrink: true } }} value={values.date} onChange={handleChange} /></Grid>
                    <Grid item xs={12} sm={4}><TextField fullWidth label="Projet" name="projet" value={values.projet} onChange={handleChange} /></Grid>
                  </Grid>
                </Box>

                {/* Inspector Information Section */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2, p: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2, textAlign: 'center', background: '#f5f5f5', p: 1, borderRadius: 1 }}>
                    Informations de l'inspection
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Date de précédente inspection"
                        name="date_precedente_inspection"
                        type="date"
                        slotProps={{ inputLabel: { shrink: true } }}
                        value={values.date_precedente_inspection}
                        onChange={handleChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" sx={{ mt: 1, fontWeight: 'medium' }}>
                        Standard : IEC 60079-17 V2023
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Inspecteur"
                        name="inspecteur"
                        value={values.inspecteur}
                        onChange={handleChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Qualifications"
                        name="qualifications"
                        value={values.qualifications}
                        onChange={handleChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Numéro de certificat"
                        name="numero_certificat"
                        value={values.numero_certificat}
                        onChange={handleChange}
                      />
                    </Grid>
                  </Grid>
                </Box>

                {/* Informations générales & Emplacement */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2 }}>
                  <Grid container>
                    {/* Informations générales */}
                    <Grid item xs={12} md={6}>
                      <Box sx={{ background: '#f5f5f5', p: 1, borderTopLeftRadius: 4, textAlign: 'center' }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>Informations générales</Typography>
                      </Box>
                      <Box sx={{ p: 2 }}>
                        <Grid container spacing={2}>

                          {/* Company Selection */}
                          <Grid item xs={12}>
                            <FormControl fullWidth>
                              <InputLabel id="company-label">Entreprise inspectée</InputLabel>
                              <Select
                                labelId="company-label"
                                name="inspected_company_name"
                                value={values.inspected_company_name}
                                onChange={handleChange}
                                label="Entreprise inspectée"
                                sx={{
                                  minWidth: '200px',
                                  '& .MuiSelect-select': {
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                  },
                                }}
                                MenuProps={{
                                  PaperProps: {
                                    style: {
                                      maxHeight: 300,
                                      width: 400,
                                      boxShadow: '0px 4px 12px rgba(0,0,0,0.1)',
                                    },
                                  },
                                }}
                              >
                                <MenuItem value="">
                                  <em>Sélectionner une entreprise...</em>
                                </MenuItem>
                                {companies.map((company) => (
                                  <MenuItem key={company.id} value={company.name}>
                                    {company.name}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          </Grid>

                          {/* Equipment Selection */}
                          <Grid item xs={12}>
                            <FormControl fullWidth>
                              <InputLabel id="equipement-label">Équipement</InputLabel>
    <Select
      labelId="equipement-label"
      name="equipement"
      value={values.equipement}
      onChange={handleChange}
      label="Équipement"
      sx={{
        minWidth: '200px',
        '& .MuiSelect-select': {
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
      }}
      MenuProps={{
        PaperProps: {
          style: {
            maxHeight: 300,
            width: 400, // ← Wider than Unité
            boxShadow: '0px 4px 12px rgba(0,0,0,0.1)',
            left: 'auto',
            right: 0,
          },
        },
        anchorOrigin: {
          vertical: 'bottom',
          horizontal: 'right',
        },
        transformOrigin: {
          vertical: 'top',
          horizontal: 'right',
        },
        getContentAnchorEl: null,
      }}
    >
      <MenuItem value="">
        <em>Sélectionner...</em>
      </MenuItem>
      <MenuItem value="Boite de jonction">Boîte de jonction</MenuItem>
      <MenuItem value="Luminaire">Luminaire</MenuItem>
      <MenuItem value="Moteur">Moteur</MenuItem>
      <MenuItem value="Boite de commande">Boîte de commande</MenuItem>
      <MenuItem value="Transmetteur (de pression, de température ou de débit)">Transmetteur (de pression, de température ou de débit)</MenuItem>
      <MenuItem value="Bouton d’arrêt d’urgence">Bouton d’arrêt d’urgence</MenuItem>
      <MenuItem value="Débitmètre">Débitmètre</MenuItem>
      <MenuItem value="Armoire électrique">Armoire électrique</MenuItem>
      <MenuItem value="Pompe">Pompe</MenuItem>
      <MenuItem value="Sonde de température">Sonde de température</MenuItem>
      <MenuItem value="Prise de courant">Prise de courant</MenuItem>
      <MenuItem value="Positionneur de vanne">Positionneur de vanne</MenuItem>
      <MenuItem value="Contrôleur de niveau">Contrôleur de niveau</MenuItem>
       <MenuItem value="Chauffage ou Système de Chauffage">Chauffage ou Système de Chauffage</MenuItem>
      <MenuItem value="Autre (switch, limit switch SOV, switch électrique…)">Autre (switch, limit switch SOV, switch électrique…)</MenuItem>
      <MenuItem value="custom">➕ Ajouter un nouvel équipement</MenuItem>
    </Select>
  </FormControl>
  {values.equipement === 'custom' && (
    <TextField
      fullWidth
      label="Nom du nouvel équipement"
      name="equipement_custom"
      value={values.equipement_custom || ''}
      onChange={(e) => {
        handleChange(e);
        setFieldValue('equipement', e.target.value);
      }}
      sx={{ mt: 2 }}
      placeholder="Entrez le nom du nouvel équipement"
    />
  )}
</Grid>



                          <Grid item xs={12}><TextField fullWidth label="Repère / TAG" name="tag" value={values.tag} onChange={handleChange} /></Grid>
                          <Grid item xs={12}><TextField fullWidth label="Constructeur" name="constructeur" value={values.constructeur} onChange={handleChange} /></Grid>
                          <Grid item xs={12}><TextField fullWidth label="Model / Type" name="model" value={values.model} onChange={handleChange} /></Grid>
                          <Grid item xs={6}><TextField fullWidth label="N° de série" name="numero_serie" value={values.numero_serie} onChange={handleChange} /></Grid>
                          <Grid item xs={6}><TextField fullWidth label="Date d'installation" name="date_installation" type="date" slotProps={{ inputLabel: { shrink: true } }} value={values.date_installation} onChange={handleChange} /></Grid>
                          <Grid item xs={12}><TextField fullWidth label="Age" name="age" value={values.age} onChange={handleChange} /></Grid>
                          <Grid item xs={4}><TextField fullWidth label="P (W)" name="puissance" value={values.puissance} onChange={handleChange} /></Grid>
                          <Grid item xs={4}><TextField fullWidth label="I (A)" name="courant" value={values.courant} onChange={handleChange} /></Grid>
                          <Grid item xs={4}><TextField fullWidth label="U (V)" name="tension" value={values.tension} onChange={handleChange} /></Grid>
                        </Grid>
                      </Box>
                    </Grid>

                    {/* Emplacement */}
                    <Grid item xs={12} md={6}>
                      <Box sx={{ background: '#f5f5f5', p: 1, borderTopRightRadius: 4, textAlign: 'center' }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>Emplacement</Typography>
                      </Box>
                      <Box sx={{ p: 2 }}>
                        <Grid container spacing={2}>
                        



<Grid item xs={12}>
  <FormControl fullWidth>
    <InputLabel id="unite-label">Unité</InputLabel>
    <Select
      labelId="unite-label"
      name="unite"
      value={values.unite}
      onChange={handleChange}
      label="Unité"
      sx={{
        minWidth: '200px',
        '& .MuiSelect-select': {
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
      }}
      MenuProps={{
        PaperProps: {
          style: {
            maxHeight: 300,
            width: 350,
            boxShadow: '0px 4px 12px rgba(0,0,0,0.1)',
            left: 'auto',
            right: 0,
          },
        },
        anchorOrigin: {
          vertical: 'bottom',
          horizontal: 'right',
        },
        transformOrigin: {
          vertical: 'top',
          horizontal: 'right',
        },
        getContentAnchorEl: null,
      }}
    >
      <MenuItem value="">
        <em>Sélectionner...</em>
      </MenuItem>
      <MenuItem value="Manifold d’entrée (Oil production manifold)">Manifold d’entrée (Oil production manifold)</MenuItem>
      <MenuItem value="Gas injection Manifold">Gas injection Manifold</MenuItem>
      <MenuItem value="PV Manifold (Oil production manifold)">PV Manifold (Oil production manifold)</MenuItem>
      <MenuItem value="Section séparation – Ph0">Section séparation – Ph0</MenuItem>
      <MenuItem value="Section séparation – Ph1">Section séparation – Ph1</MenuItem>
      <MenuItem value="Compresseur X101A">Compresseur X101A</MenuItem>
      <MenuItem value="Compresseur X101B">Compresseur X101B</MenuItem>
      <MenuItem value="Compresseur X101C">Compresseur X101C</MenuItem>
      <MenuItem value="Compresseur X101D">Compresseur X101D</MenuItem>
      <MenuItem value="Compresseur X101E">Compresseur X101E</MenuItem>
      <MenuItem value="Compresseur X112A">Compresseur X112A</MenuItem>
      <MenuItem value="Compresseur X110">Compresseur X110</MenuItem>
      <MenuItem value="Générateur X-314">Générateur X-314</MenuItem>
      <MenuItem value="Générateur X-315">Générateur X-315</MenuItem>
      <MenuItem value="Unité d’huile chaude">Unité d’huile chaude</MenuItem>
      <MenuItem value="Unité fuel gaz">Unité fuel gaz</MenuItem>
      <MenuItem value="Rack. Zone de stockage de brut">Rack. Zone de stockage de brut</MenuItem>
      <MenuItem value="Zone d’expédition">Zone d’expédition</MenuItem>
      <MenuItem value="Station de service">Station de service</MenuItem>
      <MenuItem value="Rack à côté d’unité fire water">Rack à côté d’unité fire water</MenuItem>
      <MenuItem value="Vanne ESDV Externe">Vanne ESDV Externe</MenuItem>
      <MenuItem value="custom">➕ Ajouter une nouvelle unité</MenuItem>
    </Select>
  </FormControl>
  {values.unite === 'custom' && (
    <TextField
      fullWidth
      label="Nom de la nouvelle unité"
      name="unite_custom"
      value={values.unite_custom || ''}
      onChange={(e) => {
        handleChange(e);
        setFieldValue('unite', e.target.value);
      }}
      sx={{ mt: 2 }}
      placeholder="Entrez le nom de la nouvelle unité"
    />
  )}
</Grid>












                          <Grid item xs={12}><TextField fullWidth label="Localisation" name="localisation" value={values.localisation} onChange={handleChange} /></Grid>
                          <Grid item xs={12}><TextField fullWidth label="Zone ATEX" name="zone_atex" value={values.zone_atex} onChange={handleChange} /></Grid>
                          <Grid item xs={12}><TextField fullWidth label="Groupe de gaz" name="groupe_gaz" value={values.groupe_gaz} onChange={handleChange} /></Grid>
                          <Grid item xs={12}><TextField fullWidth label="Classe de T" name="classe_t" value={values.classe_t} onChange={handleChange} /></Grid>
                        </Grid>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>

                {/* Informations spécifiques ATEX */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2 }}>
                  <Box sx={{ background: '#f5f5f5', p: 1, borderTopLeftRadius: 4, borderTopRightRadius: 4, textAlign: 'center' }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>Informations spécifiques ATEX</Typography>
                  </Box>
                  <Grid container spacing={2} sx={{ p: 2 }}>
                    <Grid item xs={12} sm={6}><TextField fullWidth label="Marquage ATEX G" name="marquage_atex_g" value={values.marquage_atex_g} onChange={handleChange} /></Grid>
                    <Grid item xs={12} sm={6}><TextField fullWidth label="Marquage ATEX D" name="marquage_atex_d" value={values.marquage_atex_d} onChange={handleChange} /></Grid>

                 {/* Marquage US - Single field */}
<Grid item xs={12}>
  <TextField
    fullWidth
    label="Marquage US"
    name="marquage_us"
    value={values.marquage_us}
    onChange={handleChange}
    placeholder="CL,DIV,GR.   CL,DIV,GR.   CL,DIV"
    sx={{
      '& .MuiInputBase-input': {
        // Optional: keep main input text at default or set a size
        fontSize: '1rem' // adjust if needed
      },
      '& .MuiInputBase-input::placeholder': {
        fontSize: '0.75rem', // smaller placeholder text
        color: '#bbb',
        opacity: 0.7,
        fontStyle: 'italic'
      }
    }}
  />
</Grid>


                    {/* Type de marquage and Mode de protection - Dedicated row for better visibility */}
                    <Grid item xs={12} sm={4}>
                      <TextField fullWidth label="Type de marquage" name="type_marquage" value={values.type_marquage} onChange={handleChange} />
                    </Grid>
                   <Grid item xs={12} sm={8}>
  <FormControl fullWidth>
    <InputLabel id="mode-protection-label">Mode de protection</InputLabel>
    <Select
      labelId="mode-protection-label"
      name="mode_protection"
      value={values.mode_protection}
      onChange={handleChange}
      label="Mode de protection"
      sx={{
        minWidth: '200px', // Ensures dropdown options have enough width
        '& .MuiSelect-select': {
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
      }}
      MenuProps={{
        PaperProps: {
          style: {
            maxHeight: 300, // limit height if needed
            width: 250,     // set a fixed width for dropdown menu
            boxShadow: '0px 4px 12px rgba(0,0,0,0.1)',
          },
        },
      }}
    >
      <MenuItem value="">
        <em>Sélectionner...</em>
      </MenuItem>
      <MenuItem value="d">d</MenuItem>
      <MenuItem value="e">e</MenuItem>
      <MenuItem value="n">n</MenuItem>
      <MenuItem value="t">t</MenuItem>
      <MenuItem value="tD">tD</MenuItem>
      <MenuItem value="i">i</MenuItem>
      <MenuItem value="o">o</MenuItem>
      <MenuItem value="p">p</MenuItem>
      <MenuItem value="pD">pD</MenuItem>
    </Select>
  </FormControl>
</Grid>

                    <Grid item xs={12} sm={6}><TextField fullWidth label="N° organisme notifié" name="organisme_notifie" value={values.organisme_notifie} onChange={handleChange} /></Grid>
                    <Grid item xs={12} sm={6}><TextField fullWidth label="IP" name="ip" value={values.ip} onChange={handleChange} /></Grid>
                    <Grid item xs={12} sm={6}><TextField fullWidth label="N° de certificat" name="certificat" value={values.certificat} onChange={handleChange} /></Grid>
                    <Grid item xs={12} sm={6}><TextField fullWidth label="NEMA" name="nema" value={values.nema} onChange={handleChange} /></Grid>

                    {/* Temperature range */}
                    <Grid item xs={12}>
                      <Grid container spacing={1} alignItems="center">
                        <Grid item xs={3}><TextField fullWidth label="T amb min" name="tamb_min" value={values.tamb_min} onChange={handleChange} /></Grid>
                        <Grid item xs={1} sx={{ textAlign: 'center' }}><Typography>≤ T amb ≤</Typography></Grid>
                        <Grid item xs={3}><TextField fullWidth label="T amb max" name="tamb_max" value={values.tamb_max} onChange={handleChange} /></Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Box>

                {/* Équipement adéquat à la zone ATEX */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2, p: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>Équipement adéquat à la zone ATEX</Typography>
                  <FormGroup row>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={values.atex_oui}
                          onChange={e => {
                            setFieldValue('atex_oui', e.target.checked);
                            if (e.target.checked) setFieldValue('atex_non', false);
                          }}
                        />
                      }
                      label="Oui"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={values.atex_non}
                          onChange={e => {
                            setFieldValue('atex_non', e.target.checked);
                            if (e.target.checked) setFieldValue('atex_oui', false);
                          }}
                        />
                      }
                      label="Non"
                    />
                  </FormGroup>
                </Box>

                {/* Inspection Visuelle et de Près - Photos de l'équipement */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2, p: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>Inspection Visuelle et de Près</Typography>
                  <Typography variant="body2" mb={1}>Photos de l'équipement</Typography>
                  <FormGroup row>
                    <FormControlLabel control={<Checkbox checked={values.acces_inaccessible} onChange={e => setFieldValue('acces_inaccessible', e.target.checked)} />} label="Appareil Inaccessible" />
                    <FormControlLabel control={<Checkbox checked={values.acces_calorifuge} onChange={e => setFieldValue('acces_calorifuge', e.target.checked)} />} label="Appareil sous Calorifuge / Ignifuge" />
                    <FormControlLabel control={<Checkbox checked={values.acces_peinte} onChange={e => setFieldValue('acces_peinte', e.target.checked)} />} label="Plaque Peinte" />
                    <FormControlLabel control={<Checkbox checked={values.acces_inaccessible_plaque} onChange={e => setFieldValue('acces_inaccessible_plaque', e.target.checked)} />} label="Plaque Inaccessible" />
                    <FormControlLabel control={<Checkbox checked={values.acces_illisible} onChange={e => setFieldValue('acces_illisible', e.target.checked)} />} label="Plaque Illisible" />
                    <FormControlLabel control={<Checkbox checked={values.acces_pas_plaque} onChange={e => setFieldValue('acces_pas_plaque', e.target.checked)} />} label="Pas de plaque" />
                  </FormGroup>
                </Box>



                {/* Sélection des niveaux d'inspection */}
                <Box sx={{ border: '2px solid #1976d2', borderRadius: 1, mb: 3, p: 2, backgroundColor: '#f8f9ff' }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>
                    Sélection des niveaux d'inspection
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2, color: '#666' }}>
                    Veuillez sélectionner un ou plusieurs niveaux d'inspection pour afficher les points à vérifier correspondants.

                    
                  </Typography>
                                   {/* Enhanced Niveaux Explanation */}
                  <Box sx={{ mt: 2, p: 2, backgroundColor: '#ffffff', borderRadius: 2, boxShadow: 1 }}>
                    <Grid container spacing={2}>
                      {/* Niveau 1 */}
                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                          <Box
                            sx={{
                              backgroundColor: '#e3f2fd',
                              color: '#1976d2',
                              fontWeight: 'bold',
                              borderRadius: '50%',
                              width: 30,
                              height: 30,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              flexShrink: 0,
                              mr: 2,
                              mt: 0.5,
                            }}
                          >
                            1
                          </Box>
                          <Box>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                              Niveau 1 (Inspection visuelle V)
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#555' }}>
                              Inspection qui permet de détecter, sans l’utilisation d’un matériel d’accès ou d’outils, les défectuosités visibles à l’œil nu, telles que des boulons manquants.
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>

                      {/* Niveau 2 */}
                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                          <Box
                            sx={{
                              backgroundColor: '#fff3e0',
                              color: '#f57c00',
                              fontWeight: 'bold',
                              borderRadius: '50%',
                              width: 30,
                              height: 30,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              flexShrink: 0,
                              mr: 2,
                              mt: 0.5,
                            }}
                          >
                            2
                          </Box>
                          <Box>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#f57c00' }}>
                              Niveau 2 (Inspection de près P)
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#555' }}>
                              Inspection qui comporte les aspects couverts par une inspection visuelle et, de plus, détecte les défectuosités, telles que des boulons desserrés, qui ne peuvent être mises en évidence que par l’utilisation d’un équipement d’accès, par exemple des échelles (quand cela est nécessaire) et des outils. L’inspection de près n’exige pas que l’enveloppe soit ouverte, ni que le matériel soit mis hors tension.
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>

                      {/* Niveau 3 */}
                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                          <Box
                            sx={{
                              backgroundColor: '#fce4ec',
                              color: '#c2185b',
                              fontWeight: 'bold',
                              borderRadius: '50%',
                              width: 30,
                              height: 30,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              flexShrink: 0,
                              mr: 2,
                              mt: 0.5,
                            }}
                          >
                            3
                          </Box>
                          <Box>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: '#c2185b' }}>
                              Niveau 3 (Inspection détaillée D)
                            </Typography>
                            <Typography variant="body2" sx={{ color: '#555' }}>
                              Inspection qui comporte les aspects couverts par une inspection de près et qui, de plus, détecte les défectuosités, telles que des connexions desserrées, qui ne sont détectables qu’après ouverture de l’enveloppe et/ou en utilisant, quand cela est nécessaire, des outils et appareillages d’essai. Généralement cette inspection exige que le matériel soit déconnecté des sources d’énergie.
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                  <FormGroup row>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={values.niveau_1}
                          onChange={e => setFieldValue('niveau_1', e.target.checked)}
                          sx={{ color: '#1976d2' }}
                        />
                      }
                      label="Niveau 1 "
                      sx={{ mr: 4 }}
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={values.niveau_2}
                          onChange={e => setFieldValue('niveau_2', e.target.checked)}
                          sx={{ color: '#1976d2' }}
                        />
                      }
                      label="Niveau 2"
                      sx={{ mr: 4 }}
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={values.niveau_3}
                          onChange={e => setFieldValue('niveau_3', e.target.checked)}
                          sx={{ color: '#1976d2' }}
                        />
                      }
                      label="Niveau 3"
                    />
                  </FormGroup>
                </Box>

                {/* Points à vérifier - Only show when at least one niveau is selected */}
                {(values.niveau_1 || values.niveau_2 || values.niveau_3) ? (
                  <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2, p: 2 }}>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 3, color: '#1976d2' }}>
                      Points à vérifier
                    </Typography>

                    {/* Show message if no mode of protection is selected */}
                    {!values.mode_protection && (
                      <Box sx={{
                        p: 3,
                        backgroundColor: '#fff3cd',
                        border: '1px solid #ffeaa7',
                        borderRadius: 1,
                        mb: 3,
                        textAlign: 'center'
                      }}>
                        <Typography variant="h6" sx={{ color: '#856404', mb: 1 }}>
                          ⚠️ Mode de protection requis
                        </Typography>
                        <Typography variant="body1" sx={{ color: '#856404' }}>
                          Veuillez sélectionner un mode de protection dans la section "Informations spécifiques ATEX"
                          pour afficher les points d'inspection correspondants.
                        </Typography>
                      </Box>
                    )}

                    {/* Check if any sections will be displayed */}
                    {(() => {
                      const modeProtection = values.mode_protection;
                      const visibleSections = pointsSections.filter(section => {
                        if (!modeProtection) return false;

                        // Check if section title contains the mode protection patterns
                        if (section.title.includes('Ex "d", Ex "e", Ex "n" et Ex "t/tD"')) {
                          return ['d', 'e', 'n', 't', 'tD'].includes(modeProtection);
                        }
                        if (section.title.includes('Ex "i"')) {
                          return modeProtection === 'i';
                        }
                        if (section.title.includes('Ex "o"')) {
                          return modeProtection === 'o';
                        }
                        if (section.title.includes('Ex "p" et "pD"')) {
                          return ['p', 'pD'].includes(modeProtection);
                        }

                        return false;
                      });

                      // Show message if mode is selected but no sections match
                      if (modeProtection && visibleSections.length === 0) {
                        return (
                          <Box sx={{
                            p: 3,
                            backgroundColor: '#f8d7da',
                            border: '1px solid #f5c6cb',
                            borderRadius: 1,
                            mb: 3,
                            textAlign: 'center'
                          }}>
                            <Typography variant="h6" sx={{ color: '#721c24', mb: 1 }}>
                              ℹ️ Aucune section disponible
                            </Typography>
                            <Typography variant="body1" sx={{ color: '#721c24' }}>
                              Aucune section d'inspection n'est disponible pour le mode de protection "{modeProtection}".
                              Veuillez vérifier votre sélection.
                            </Typography>
                          </Box>
                        );
                      }

                      // Show section count if sections are available
                      if (modeProtection && visibleSections.length > 0) {
                        return (
                          <Box sx={{
                            p: 2,
                            backgroundColor: '#d4edda',
                            border: '1px solid #c3e6cb',
                            borderRadius: 1,
                            mb: 3,
                            textAlign: 'center'
                          }}>
                            <Typography variant="body1" sx={{ color: '#155724', fontWeight: 'bold' }}>
                              ✅ Mode de protection "{modeProtection}" sélectionné - {visibleSections.length} section(s) d'inspection disponible(s)
                            </Typography>
                          </Box>
                        );
                      }

                      return null;
                    })()}

                    {pointsSections.map((section, sectionIndex) => {
                      // Filter points based on selected niveaux
                      const selectedNiveaux = [];
                      if (values.niveau_1) selectedNiveaux.push(1);
                      if (values.niveau_2) selectedNiveaux.push(2);
                      if (values.niveau_3) selectedNiveaux.push(3);

                      // Check if this section should be displayed based on mode of protection
                      const shouldShowSection = () => {
                        const modeProtection = values.mode_protection;

                        // If no mode is selected, don't show any sections
                        if (!modeProtection) {
                          return false;
                        }

                        // Check if section title contains the mode protection patterns
                        if (section.title.includes('Ex "d", Ex "e", Ex "n" et Ex "t/tD"')) {
                          return ['d', 'e', 'n', 't', 'tD'].includes(modeProtection);
                        }
                        if (section.title.includes('Ex "i"')) {
                          return modeProtection === 'i';
                        }
                        if (section.title.includes('Ex "o"')) {
                          return modeProtection === 'o';
                        }
                        if (section.title.includes('Ex "p" et "pD"')) {
                          return ['p', 'pD'].includes(modeProtection);
                        }

                        return false; // Don't show any other sections
                      };

                      // Don't render the section if it shouldn't be shown
                      if (!shouldShowSection()) {
                        return null;
                      }

                      return (
                        <Box key={sectionIndex} sx={{ mb: 4 }}>
                          {/* Main section title */}
                          <Typography variant="h5" sx={{
                            fontWeight: 'bold',
                            mb: 3,
                            color: '#1976d2',
                            backgroundColor: '#e3f2fd',
                            p: 2,
                            borderRadius: 1,
                            border: '2px solid #1976d2'
                          }}>
                            {section.title}
                          </Typography>

                          {/* Subsections */}
                          {section.subsections.map((subsection, subsectionIndex) => {
                            // Check if MOTEURS subsection should be shown based on equipment type
                            if (subsection.subtitle === 'MATÉRIEL PARTICULIER (MOTEURS)') {
                              const equipement = values.equipement || '';
                              const equipementCustom = values.equipement_custom || '';

                              // Check if equipment is motor or pump related
                              const motorKeywords = ['moteur', 'pompe', 'motor', 'pump'];
                              let isMotorEquipment = false;

                              // Check main equipment field
                              if (motorKeywords.some(keyword => equipement.toLowerCase().includes(keyword.toLowerCase()))) {
                                isMotorEquipment = true;
                              }

                              // Check custom equipment field if equipment is 'custom'
                              if (equipement.toLowerCase() === 'custom' && equipementCustom) {
                                if (motorKeywords.some(keyword => equipementCustom.toLowerCase().includes(keyword.toLowerCase()))) {
                                  isMotorEquipment = true;
                                }
                              }

                              // Skip this subsection if not motor/pump equipment
                              if (!isMotorEquipment) {
                                return null;
                              }
                            }

                            // Check if ÉCLAIRAGE subsection should be shown based on equipment type
                            if (subsection.subtitle === 'MATÉRIEL PARTICULIER (ÉCLAIRAGE)') {
                              const equipement = values.equipement || '';
                              const equipementCustom = values.equipement_custom || '';

                              // Check if equipment is luminaire related
                              const luminaireKeywords = ['luminaire', 'éclairage', 'eclairage', 'lighting', 'lamp', 'lampe'];
                              let isLuminaireEquipment = false;

                              // Check main equipment field
                              if (luminaireKeywords.some(keyword => equipement.toLowerCase().includes(keyword.toLowerCase()))) {
                                isLuminaireEquipment = true;
                              }

                              // Check custom equipment field if equipment is 'custom'
                              if (equipement.toLowerCase() === 'custom' && equipementCustom) {
                                if (luminaireKeywords.some(keyword => equipementCustom.toLowerCase().includes(keyword.toLowerCase()))) {
                                  isLuminaireEquipment = true;
                                }
                              }

                              // Skip this subsection if not luminaire equipment
                              if (!isLuminaireEquipment) {
                                return null;
                              }
                            }

                            const visiblePoints = subsection.points.filter(point => {
                              // First check niveau filtering
                              const niveauMatch = point.niveaux.length === 0 || point.niveaux.some(niveau => selectedNiveaux.includes(niveau));

                              if (!niveauMatch) return false;

                              // Then check mode of protection filtering
                              const modeProtection = values.mode_protection;
                              if (!modeProtection) return false;

                              return pointAppliesToMode(point, modeProtection);
                            });

                            if (visiblePoints.length === 0) return null;

                            return (
                              <Box key={subsectionIndex} sx={{ mb: 3, border: '1px solid #e0e0e0', borderRadius: 1, p: 2 }}>
                                <Typography variant="h6" sx={{
                                  fontWeight: 'bold',
                                  mb: 2,
                                  color: '#1976d2',
                                  backgroundColor: '#f5f5f5',
                                  p: 1,
                                  borderRadius: 1
                                }}>
                                  {subsection.subtitle}
                                </Typography>
                                <TableContainer>
                                  <Table size="small">
                                    <TableHead>
                                      <TableRow sx={{ backgroundColor: '#f8f9ff' }}>
                                        <TableCell sx={{ fontWeight: 'bold' }}>Point à vérifier</TableCell>
                                        <TableCell align="center" sx={{ fontWeight: 'bold' }}>Correct</TableCell>
                                        <TableCell align="center" sx={{ fontWeight: 'bold' }}>Défaut</TableCell>
                                        <TableCell align="center" sx={{ fontWeight: 'bold' }}>F</TableCell>
                                        <TableCell align="center" sx={{ fontWeight: 'bold' }}>G</TableCell>
                                        <TableCell align="center" sx={{ fontWeight: 'bold' }}>C</TableCell>
                                        <TableCell sx={{ fontWeight: 'bold' }}>Remarques</TableCell>
                                      </TableRow>
                                    </TableHead>
                                    <TableBody>
                                      {visiblePoints.map(point => (
                                        <TableRow key={point.key} sx={{ '&:hover': { backgroundColor: '#f8f9ff' } }}>
                                          <TableCell sx={{ fontWeight: 'medium' }}>
                                            {point.label}
                                            {point.niveaux.length > 0 && (
                                              <Typography variant="caption" sx={{
                                                display: 'block',
                                                color: '#666',
                                                fontStyle: 'italic'
                                              }}>
                                                (Niveau{point.niveaux.length > 1 ? 'x' : ''}: {point.niveaux.join(', ')})
                                              </Typography>
                                            )}
                                          </TableCell>
                                          <TableCell align="center">
                                            <Checkbox
                                              checked={values.points[point.key].correct}
                                              onChange={e => {
                                                setFieldValue(`points.${point.key}.correct`, e.target.checked);
                                                if (e.target.checked) {
                                                  setFieldValue(`points.${point.key}.defaut`, false);
                                                }
                                              }}
                                              sx={{ color: '#4C9A2a' }}
                                            />
                                          </TableCell>
                                          <TableCell align="center">
                                            <Checkbox
                                              checked={values.points[point.key].defaut}
                                              onChange={e => {
                                                setFieldValue(`points.${point.key}.defaut`, e.target.checked);
                                                if (e.target.checked) {
                                                  setFieldValue(`points.${point.key}.correct`, false);
                                                }
                                              }}
                                              sx={{ color: '#f44336' }}
                                            />
                                          </TableCell>
                                          <TableCell align="center">
                                            <FormControl size="small" sx={{ minWidth: 80 }}>
                                              <Select
                                                value={values.points[point.key]?.f || ''}
                                                onChange={(e) => setFieldValue(`points.${point.key}.f`, e.target.value)}
                                                displayEmpty
                                              >
                                                <MenuItem value="F1">F1</MenuItem>
                                                <MenuItem value="F2">F2</MenuItem>
                                                <MenuItem value="F3">F3</MenuItem>
                                                <MenuItem value="F4">F4</MenuItem>
                                                <MenuItem value="F5">F5</MenuItem>
                                              </Select>
                                            </FormControl>
                                          </TableCell>
                                          <TableCell align="center">
                                            <FormControl size="small" sx={{ minWidth: 80 }}>
                                              <Select
                                                value={values.points[point.key]?.g || ''}
                                                onChange={(e) => setFieldValue(`points.${point.key}.g`, e.target.value)}
                                                displayEmpty
                                              >
                                                <MenuItem value="G1">G1</MenuItem>
                                                <MenuItem value="G2">G2</MenuItem>
                                                <MenuItem value="G3">G3</MenuItem>
                                                <MenuItem value="G4">G4</MenuItem>
                                                <MenuItem value="G5">G5</MenuItem>
                                              </Select>
                                            </FormControl>
                                          </TableCell>
                                          <TableCell align="center">
                                            <FormControl size="small" sx={{ minWidth: 80 }}>
                                              <Select
                                                value={values.points[point.key]?.c || ''}
                                                onChange={(e) => setFieldValue(`points.${point.key}.c`, e.target.value)}
                                                displayEmpty
                                                renderValue={(selected) => {
                                                  if (!selected) return '';
                                                  return (
                                                    <Box
                                                      sx={{
                                                        width: 20,
                                                        height: 20,
                                                        backgroundColor: selected,
                                                        border: '1px solid #ccc',
                                                        borderRadius: 1,
                                                        margin: 'auto'
                                                      }}
                                                    />
                                                  );
                                                }}
                                              >
                                                <MenuItem value="#ff0000">
                                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Box
                                                      sx={{
                                                        width: 20,
                                                        height: 20,
                                                        backgroundColor: '#ff0000',
                                                        border: '1px solid #ccc',
                                                        borderRadius: 1
                                                      }}
                                                    />
                                                  </Box>
                                                </MenuItem>
                                                <MenuItem value="#FFFF00">
                                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Box
                                                      sx={{
                                                        width: 20,
                                                        height: 20,
                                                        backgroundColor: '#FFFF00',
                                                        border: '1px solid #ccc',
                                                        borderRadius: 1
                                                      }}
                                                    />
                                                  </Box>
                                                </MenuItem>
                                                <MenuItem value="#FFA500">
                                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Box
                                                      sx={{
                                                        width: 20,
                                                        height: 20,
                                                        backgroundColor: '#FFA500',
                                                        border: '1px solid #ccc',
                                                        borderRadius: 1
                                                      }}
                                                    />
                                                  </Box>
                                                </MenuItem>
                                                <MenuItem value="#4C9A2a">
                                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Box
                                                      sx={{
                                                        width: 20,
                                                        height: 20,
                                                        backgroundColor: '#4C9A2a',
                                                        border: '1px solid #ccc',
                                                        borderRadius: 1
                                                      }}
                                                    />
                                                  </Box>
                                                </MenuItem>
                                              </Select>
                                            </FormControl>
                                          </TableCell>
                                          <TableCell>
                                            <TextField
                                              fullWidth
                                              size="small"
                                              name={`points.${point.key}.remarques`}
                                              value={values.points[point.key].remarques}
                                              onChange={handleChange}
                                              placeholder="Remarques..."
                                            />
                                          </TableCell>
                                        </TableRow>
                                      ))}
                                    </TableBody>
                                  </Table>
                                </TableContainer>
                              </Box>
                            );
                          })}
                        </Box>
                      );
                    })}
                  </Box>
                ) : (
                  <Box sx={{
                    border: '2px dashed #ccc',
                    borderRadius: 1,
                    mb: 2,
                    p: 4,
                    textAlign: 'center',
                    backgroundColor: '#f9f9f9'
                  }}>
                    <Typography variant="h6" sx={{ color: '#666', mb: 1 }}>
                      Points à vérifier
                    </Typography>
                    <Typography variant="body1" sx={{ color: '#999' }}>
                      Veuillez sélectionner au moins un niveau d'inspection pour afficher les points à vérifier.
                    </Typography>
                  </Box>
                )}

                {/* Photos de l'équipement - Static section */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2, p: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>Photos de l'équipement</Typography>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 1 }}>Une photo du marquage</Typography>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={async (e) => {
                        if (e.target.files[0]) {
                          const file = e.target.files[0];
                          console.log(`Original photo_marquage size: ${(file.size / 1024 / 1024).toFixed(2)}MB`);

                          // Compress image if it's large
                          let processedFile = file;
                          if (file.size > 2 * 1024 * 1024) { // 2MB
                            console.log('Compressing photo_marquage...');
                            const compressed = await compressImage(file, 1200, 0.7);
                            processedFile = new File([compressed], file.name, { type: 'image/jpeg' });
                            console.log(`Compressed photo_marquage size: ${(processedFile.size / 1024 / 1024).toFixed(2)}MB`);
                          }

                          setFieldValue('photo_marquage', processedFile);
                        } else {
                          // Remove the field from form state if no file is selected
                          setFieldValue('photo_marquage', undefined);
                        }
                      }}
                      style={{ marginBottom: '8px', width: '100%' }}
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Photo claire du marquage ATEX/certification de l'équipement
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 1 }}>Une photo de l'équipement</Typography>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={async (e) => {
                        if (e.target.files[0]) {
                          const file = e.target.files[0];
                          console.log(`Original photo_equipement size: ${(file.size / 1024 / 1024).toFixed(2)}MB`);

                          // Compress image if it's large
                          let processedFile = file;
                          if (file.size > 2 * 1024 * 1024) { // 2MB
                            console.log('Compressing photo_equipement...');
                            const compressed = await compressImage(file, 1200, 0.7);
                            processedFile = new File([compressed], file.name, { type: 'image/jpeg' });
                            console.log(`Compressed photo_equipement size: ${(processedFile.size / 1024 / 1024).toFixed(2)}MB`);
                          }

                          setFieldValue('photo_equipement', processedFile);
                        } else {
                          // Remove the field from form state if no file is selected
                          setFieldValue('photo_equipement', undefined);
                        }
                      }}
                      style={{ marginBottom: '8px', width: '100%' }}
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Photo générale de l'équipement dans son environnement
                    </Typography>
                  </Box>

                </Box>

                {/* Les anomalies - Separate Section */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2, p: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2, textAlign: 'center', background: '#f5f5f5', p: 1, borderRadius: 1 }}>
                    Les anomalies
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 1 }}>Photos des anomalies</Typography>
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={async (e) => {
                        if (e.target.files.length > 0) {
                          const files = Array.from(e.target.files);
                          console.log(`Processing ${files.length} anomaly photos`);

                          // Compress images if they're large
                          const processedFiles = await Promise.all(
                            files.map(async (file, index) => {
                              console.log(`Original anomaly photo ${index + 1} size: ${(file.size / 1024 / 1024).toFixed(2)}MB`);

                              if (file.size > 2 * 1024 * 1024) { // 2MB
                                console.log(`Compressing anomaly photo ${index + 1}...`);
                                const compressed = await compressImage(file, 1000, 0.6); // Smaller for anomalies
                                const processedFile = new File([compressed], file.name, { type: 'image/jpeg' });
                                console.log(`Compressed anomaly photo ${index + 1} size: ${(processedFile.size / 1024 / 1024).toFixed(2)}MB`);
                                return processedFile;
                              }
                              return file;
                            })
                          );

                          setFieldValue('photos_anomalies', processedFiles);
                        } else {
                          // Remove the field from form state if no files are selected
                          setFieldValue('photos_anomalies', undefined);
                        }
                      }}
                      style={{ marginBottom: '8px', width: '100%' }}
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Photos détaillées de toutes les anomalies détectées lors de l'inspection
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#1976d2', fontStyle: 'italic' }}>
                      Vous pouvez sélectionner plusieurs photos en maintenant Ctrl (Windows) ou Cmd (Mac) lors de la sélection
                    </Typography>
                  </Box>
                </Box>

                {/* Observations complémentaires */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2, p: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>Observations complémentaires</Typography>
                  <TextField
                    fullWidth
                    multiline
                    minRows={3}
                    name="observations_complementaires"
                    value={values.observations_complementaires}
                    onChange={handleChange}
                    placeholder="Observations et commentaires supplémentaires sur l'inspection..."
                    sx={{ mb: 2 }}
                  />
                </Box>

                {/* Action / Recommandation */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, mb: 2, p: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>Action / Recommandation</Typography>
                  <TextField
                    fullWidth
                    multiline
                    minRows={2}
                    name="action"
                    value={values.action}
                    onChange={handleChange}
                    sx={{ mb: 2 }}
                  />
                </Box>

                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={isSubmitting}
                    sx={{ minWidth: 200 }}
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit'}
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </Paper>
      </Container>
    </Box>
  );
};

export default InspectionFormPage;